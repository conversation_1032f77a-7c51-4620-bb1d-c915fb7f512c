import express from "express";
import cors from "cors";
import config from "./config/config";
import router from "./routes";
import errorMiddleware from "./middlewares/errorMiddleware";

const app = express();

app.use(cors());

app.use(express.json());

app.use("/api/v1",router);

app.use(errorMiddleware)

app.listen(config.PORT, () => {
    console.log(`Server is running on port ${config.PORT}`);
});
