import { prisma } from "./index";

async function main() {
  // Seed Admins
  await prisma.admin.createMany({
    data: [
      {
        name: "<PERSON>",
        email: "<EMAIL>",
        password: "123456",
      },
      {
        name: "<PERSON>",
        email: "<EMAIL>",
        password: "123456",
      },
    ],
    skipDuplicates: true,
  });

  // Seed Animals
  await prisma.animal.createMany({
    data: [
      {
        name: "<PERSON>",
        species: "Lion",
        age: 8,
        gender: "Male",
        health_status: "Healthy",
      },
      {
        name: "<PERSON><PERSON>",
        species: "<PERSON>",
        age: 7,
        gender: "Female",
        health_status: "Injured",
      },
      {
        name: "<PERSON>",
        species: "Elephant",
        age: 25,
        gender: "Female",
        health_status: "Healthy",
      },
      {
        name: "<PERSON>",
        species: "Elephant",
        age: 30,
        gender: "Male",
        health_status: "Sick",
      },
      {
        name: "Ki<PERSON>",
        species: "Giraffe",
        age: 12,
        gender: "Female",
        health_status: "Recovering",
      },
      {
        name: "<PERSON>",
        species: "Giraffe",
        age: 10,
        gender: "Male",
        health_status: "Recovering", 
      },
      {
        name: "<PERSON>",
        species: "<PERSON>",
        age: 6,
        gender: "Male",
        health_status: "Critical",
      },
      {
        name: "Maya",
        species: "<PERSON>",
        age: 5,
        gender: "Female",
        health_status: "UnderTreatment",
      },
      {
        name: "Coco",
        species: "Monkey",
        age: 3,
        gender: "Female",
        health_status: "Healthy", 
      },
      {
        name: "Charlie",
        species: "Monkey",
        age: 4,
        gender: "Male",
        health_status: "Sick", 
      },
    ],
    skipDuplicates: true,
  });
}

main()
  .then(() => {
    console.log("✅ Seed data inserted successfully");
  })
  .catch((e) => {
    console.error("❌ Error seeding data:", e);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
