

datasource db {
  provider = "postgresql" 
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

model Admin {
  id        String      @id @default(uuid())
  email     String      @unique
  name      String
  password  String
}

model Enclosure {
  id        String    @id @default(uuid())
  name      String              // e.g Big Cat Area
  type      String              // e.g "Savannah", "Aquarium", "Bird Cage"
  capacity  Int                 // e.g  2 , 8 , animals
  condition String    @default("Good") // e.g "Good", "Needs Maintenance"
  location  String?
  //animals   Animal[]            // one-to-many relation

  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
}

model Animal {
  id            String    @id @default(uuid())
  name          String
  species       String
  age           Int
  gender        Gender
  health_status HealthStatus    @default(Healthy)
  arrival_date  DateTime  @default(now())

  // relation to enclosure
  //enclosureId   String            // many-to-one relation
  //enclosure     Enclosure @relation(fields: [enclosureId], references: [id])

  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
}

enum Gender {
  Male
  Female
}

enum HealthStatus {
  Healthy
  Sick
  Injured
  Recovering
  Critical
}


