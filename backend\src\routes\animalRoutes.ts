import { Router } from "express";
import { addAnimal, deleteAnimal, getAnimal, getAnimals, updateAnimal } from "../controllers/animal";

const animalRouter = Router();

animalRouter.post("/add",addAnimal);
animalRouter.get("/?:species&:health_status&:enclosureId", getAnimals);
animalRouter.get("/:id", getAnimal);
animalRouter.patch("/:id", updateAnimal);
animalRouter.delete("/:id", deleteAnimal);

export default animalRouter;