{"name": "backend", "module": "index.ts", "type": "module", "private": true, "scripts": {"dev": "bun src/server.ts", "prisma:generate": "bunx prisma generate", "prisma:migrate": "bunx prisma migrate dev", "seed": "bun run src/prisma/seed.ts"}, "devDependencies": {"@types/bun": "latest", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "prisma": "^6.14.0", "types": "workspace:*"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"@prisma/client": "^6.14.0", "@types/bcryptjs": "^3.0.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "zod": "^4.0.17"}, "prisma": {"schema": "src/prisma/schema.prisma"}}